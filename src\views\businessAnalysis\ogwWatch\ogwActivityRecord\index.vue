<template>
  <div class="ogwActivityRecord">
    <div class="head-box">
      <ul>
        <li
          @click="toInfo('use')"
          :class="{ active: isActive === 'useChemical' }"
        >
          化学药剂使用
        </li>
        <li
          @click="toInfo('filter')"
          :class="{ active: isActive === 'filterChemical' }"
        >
          化学药剂筛选
        </li>
        <li
          @click="toInfo('evaluation')"
          :class="{ active: isActive === 'evaluateChemical' }"
        >
          化学药剂评价
        </li>
        <li
          @click="toInfo('test')"
          :class="{ active: isActive === 'testChemical' }"
        >
          分析化验
        </li>
      </ul>
    </div>
    <div class="content-box">
      <router-view></router-view>
    </div>
  </div>
</template>
<script>
export default {
  name: "ogwActivityRecord",
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.isActive = this.$route.name;
  },
  data() {
    return {
      isActive: "use",
    };
  },
  methods: {
    toInfo(name) {
      switch (name) {
        case "use":
          this.$router.push({ name: "useChemical" });
          this.isActive = "useChemical";
          break;
        case "filter":
          this.$router.push({ name: "filterChemical" });
          this.isActive = "filterChemical";
          break;
        case "evaluation":
          this.$router.push({ name: "evaluateChemical" });
          this.isActive = "evaluateChemical";
          break;
        case "test":
          this.$router.push({ name: "testChemical" });
          this.isActive = "testChemical";
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ogwActivityRecord {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .head-box {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    background: #fff;
    color: rgba(0, 0, 0, 0.65);
    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      li {
        cursor: pointer;
        margin-right: 40px;
        position: relative;
      }
    }
  }
}

.active {
  color: #1677ff;
}
.active::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background: #1677ff;
  position: absolute;
  bottom: -13px;
  left: 0;
}
</style>
