<template>
  <div>
    <Header :title="titleData" />
    <router-view :key="$route.fullPath"></router-view>
  </div>
</template>
<script>
export default {
  data() {
    return {
      style:
        window.document.documentElement.getAttribute("data-theme") || "tint",
      titleData: [
        {
          text: "设备维修分析",
          content: "内容",
          path:"equipment"
        },
        {
          text: "油气水处理分析",
          content: "内容",
          path:"oil"
        },
        {
          text: "项目管理分析",
          content: "内容",
          path:"project"
        },
        {
          text: "运力系统跟踪",
          content: "内容",
          path:"capacity"
        },
        {
          text: "油气井作业跟踪",
          content: "内容",
          path:"well"
        },
        {
          text: "库存分析",
          content: "内容",
          path:"stock"
        },
        {
          text: "业务流程监测",
          content: "内容",
          path:"process"
        },
        {
          text: "生产能力跟踪",
          content: "内容",
          path:"prodEnviTrack"
        },
      ],
    };
  },
  mounted() {
    this.startMutationObserver();
  },
  watch: {
    style: function (n) {
      this.setChartColor();
    },
  },
  methods: {
    setChartColor() {
      this.modalTypeO = this.style === "dark" ? "o" : "w";
      this.borderTypeR = "r";
    },
    startMutationObserver() {
      let rootElement = window.document.documentElement;

      // 创建一个 MutationObserver 实例
      let observer = new MutationObserver(() => {
        // 处理根元素属性变化
        let newValue = rootElement.getAttribute("data-theme");
        this.style = newValue;
      });

      // 配置 MutationObserver 监视的属性
      let config = { attributes: true };
      // 启动 MutationObserver
      observer.observe(rootElement, config);
      this.setChartColor();
    },
  },
};
</script>