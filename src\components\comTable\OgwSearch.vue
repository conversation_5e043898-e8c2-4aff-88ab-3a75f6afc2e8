<template>
  <div class="search-bar">
    <el-form :inline="true" :model="formData" class="form-body">
      <el-form-item
        v-for="(field, index) in visibleFields"
        :key="field.prop"
        :label="field.label"
        :class="[
          { 'month-range': field.type === 'monthrange' },
          { 'date-range': field.type === 'daterange' },
          { month: field.type === 'month' },
          { year: field.type === 'year' },
        ]"
      >
        <!-- Select -->
        <el-select
          :popper-append-to-body="false"
          v-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder || '请选择'"
          clearable
        >
          <el-option
            v-for="opt in (field.options || []).filter(Boolean)"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>

        <!-- Input with debounce -->
        <el-input
          v-else-if="field.type === 'input'"
          v-model="inputBuffer[field.prop]"
          :placeholder="field.placeholder || '请输入'"
          clearable
          @input="debounceInput(field.prop)"
        />

        <!-- Date:year -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'year'"
          v-model="formData[field.prop]"
          type="year"
          :placeholder="field.placeholder || '选择年份'"
          format="yyyy年"
          value-format="yyyy"
          clearable
        />

        <!-- Date: month -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'month'"
          v-model="formData[field.prop]"
          type="month"
          :placeholder="field.placeholder || '选择月份'"
          format="yyyy年MM月"
          value-format="yyyy-MM"
          clearable
        />

        <!-- Date range -->
        <el-date-picker
          :append-to-body="false"
          v-else-if="field.type === 'daterange'"
          v-model="formData[field.prop]"
          type="daterange"
          :start-placeholder="field.startPlaceholder || '开始日期'"
          :end-placeholder="field.endPlaceholder || '结束日期'"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />

        <div v-else-if="field.type === 'monthrange'">
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][0]"
            type="month"
            :placeholder="field.startPlaceholder || '开始月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
          <span style="margin: 0 5px">至</span>
          <el-date-picker
            :append-to-body="false"
            v-model="formData[field.prop][1]"
            type="month"
            :placeholder="field.endPlaceholder || '结束月份'"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            clearable
            style="width: 140px"
          />
        </div>
      </el-form-item>

      <!-- 按钮 -->
      <el-form-item class="btns">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <slot name="actions" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "SearchBar",
  props: {
    fields: {
      type: Array,
      required: true,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    baseVisibleCount: {
      type: Number,
      default: 3, // 默认展示前 N 项
    },
    debounceTime: {
      type: Number,
      default: 400, // ms
    },
  },
  data() {
    const formData = { ...this.value };
    // 确保所有字段都有初始值
    this.fields.forEach((field) => {
      if (!(field.prop in formData)) {
        formData[field.prop] =
          field.type === "daterange" || field.type === "monthrange" ? [] : "";
      }
    });

    return {
      formData,
      inputBuffer: { ...formData },
      debounceTimers: {},
    };
  },
  computed: {
    visibleFields() {
      return this.fields.filter((f) => f.visible !== false);
    },
  },
  watch: {
    value: {
      handler(val) {
        // 只更新实际变化的字段
        Object.keys(val).forEach((key) => {
          if (JSON.stringify(this.formData[key]) !== JSON.stringify(val[key])) {
            this.$set(this.formData, key, val[key]);
          }
        });
        this.inputBuffer = { ...this.formData };
      },
      immediate: true,
      deep: true,
    },
    formData: {
      handler(val) {
        this.$emit("input", { ...val });
      },
      deep: true,
    },
  },
  methods: {
    handleSearch() {
      this.$emit("search", { ...this.formData });
    },
    handleReset() {
      const cleared = {};
      this.visibleFields.forEach(
        (f) => (cleared[f.prop] = f.type === "daterange" ? [] : "")
      );
      this.formData = cleared;
      this.inputBuffer = { ...cleared };
      this.$emit("reset", { ...this.formData });
    },
    debounceInput(prop) {
      if (this.debounceTimers[prop]) clearTimeout(this.debounceTimers[prop]);
      this.debounceTimers[prop] = setTimeout(() => {
        this.formData[prop] = this.inputBuffer[prop];
      }, this.debounceTime);
    },
  },
};
</script>

<style lang="scss" scoped>
.search-bar {
  margin: 20px 0;
  padding-right: 0;
}

.btns {
  width: 260px !important;
}

::v-deep .month-range {
  width: 320px !important;
  .el-form-item__content {
    width: 100% !important;
  }
}

::v-deep .year {
  .el-date-editor.el-input {
    width: 100% !important;
  }
}

::v-deep .month {
  width: 270px !important;
}

::v-deep .el-input__inner {
  background-color: #fff !important;
  border-color: #e2e2e2 !important;
  color: #000 !important;
}

::v-deep .el-form-item {
  width: 240px;
  .el-form-item__content {
    width: 60%;
  }
}
</style>
