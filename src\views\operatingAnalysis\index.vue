<template>
  <div>
    <Header :title="titleData" />
    <router-view></router-view>
  </div>
</template>
<script>
export default {
  data() {
    return {
      style:
        window.document.documentElement.getAttribute("data-theme") || "tint",
      titleData: [
        {
          text: "经营态势",
          content: "内容",
          path:"situation"
        },
        {
          text: "主营收入",
          content: "内容",
          children:[
            {text:'油气产量',path:'oilProduction'},
            {text:'油气实现价格',path:'oilPrice'},
            {text:'销售收入',path:'salesRevenue'},
          ]
        },
        {
          text: "成本费用",
          content: "内容",
          children:[
            {text:'桶油成本',path:'cost'},
            {text:'OEPX费用',path:'OEPX'},
            {text:'SG&A费用',path:'SG&A'},
          ]
        },
         {
          text: "盈利能力",
          content: "内容",
         children:[
            {text:'盈利分析',path:'profitAnalysis'},
            {text:'盈亏平衡分析',path:'breakEvenAnalysis'},
         ]
        },
      ],
    };
  },
  mounted() {
    this.startMutationObserver();
  },
  watch: {
    style: function (n) {
      this.setChartColor();
    },
  },
  methods: {
    setChartColor() {
      this.modalTypeO = this.style === "dark" ? "o" : "w";
      this.borderTypeR = "r";
    },
    startMutationObserver() {
      let rootElement = window.document.documentElement;

      // 创建一个 MutationObserver 实例
      let observer = new MutationObserver(() => {
        // 处理根元素属性变化
        let newValue = rootElement.getAttribute("data-theme");
        this.style = newValue;
      });

      // 配置 MutationObserver 监视的属性
      let config = { attributes: true };
      // 启动 MutationObserver
      observer.observe(rootElement, config);
      this.setChartColor();
    },
  },
};
</script>