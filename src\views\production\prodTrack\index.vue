<template>
  <div class="prod-track">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
      ></OgwSearch>
    </div>
    <div class="table-container">
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['planYear','deviceNameType','productType']"
        :show-actions="true"
        :page-size="10"
        :current-page="1"
        @cell-click="handleCellClick"
      >
        <template #actions="{ row }">
          <el-button type="text" size="small" @click="toIndicator(row)">指标分析</el-button>
        </template>
      </OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
export default {
  name: "prodTrack",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    const dateArr = [];
    dateArr[0] = new Date().getFullYear() + "-" + Number(1);
    dateArr[1] = new Date().getFullYear() + "-" + (new Date().getMonth() + 1);
    this.searchForm.monthRange = dateArr;
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.getMothColumns();
  },
  data() {
    return {
      searchForm: {
        planYear: "",
        deviceNameCode: "",
        monthRange: ["", ""],
      },
      searchFields: [
        {
          label: "计划年度:",
          prop: "planYear",
          type: "year",
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [
            { label: "全部", value: "all" },
            { label: "深海一号", value: "sh1" },
            { label: "崖城13-1", value: "yc13" },
          ],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ],
      monthColumns: [],
      tableData: [
        {
          planYear: "2023",
          deviceNameType: "深海一号",
          productType: "天然气",
          unit: "亿立方米",
          dataIndex: "产量",
          totalAmount: "100",
          totalRealAmount: "90",
          totalCompletionRate: "90%",
          totalLastYearPeriod: "80",
          totalChangeRate: "10%",
          totalChallengeTarget: "100",
          planAmount1: "10",
          realAmount1: "9",
        },
        {
          planYear: "2023",
          deviceNameType: "深海一号",
          productType: "天然气",
          unit: "亿立方米",
          dataIndex: "产量",
          totalAmount: "100",
          totalRealAmount: "90",
          totalCompletionRate: "90%",
          totalLastYearPeriod: "80",
          totalChangeRate: "10%",
          totalChallengeTarget: "100",
          planAmount1: "10",
        },
        {
          planYear: "2023",
          deviceNameType: "深海一号",
          productType: "石油",
          unit: "吨",
          dataIndex: "产量",
          totalAmount: "100",
          totalRealAmount: "90",
          totalCompletionRate: "90%",
          totalLastYearPeriod: "80",
          totalChangeRate: "10%",
          totalChallengeTarget: "100",
          planAmount1: "10",
          realAmount1: "9",
        }
      ],
    };
  },
  computed: {
    columns() {
      return [
        { label: "计划年度", prop: "planYear" },
        { label: "装置平台", prop: "deviceNameType" },
        { label: "产品类型", prop: "productType" },
        { label: "计量单位", prop: "unit", editable: true },
        { label: "数据指标", prop: "dataIndex" },
        {
          label: "累计产量",
          children: [
            { label: "实际产量", prop: "totalAmount" },
            { label: "计划产量", prop: "totalRealAmount" },
            { label: "完成率", prop: "totalCompletionRate" },
            { label: "上年同期", prop: "totalLastYearPeriod" },
            { label: "变动率", prop: "totalChangeRate" },
            { label: "挑战目标", prop: "totalChallengeTarget" },
          ],
        },
        ...this.monthColumns,
      ];
    },
  },
  methods: {
    getMothColumns() {
      const months = [
        "一月",
        "二月",
        "三月",
        "四月",
        "五月",
        "六月",
        "七月",
        "八月",
        "九月",
        "十月",
        "十一月",
        "十二月",
      ];

      // 根据选择的月份生成对应的列
      const start = this.searchForm.monthRange[0].split("-")[1];
      const end = this.searchForm.monthRange[1].split("-")[1];
      const targetMonth = months.slice(start - 1, end);

      // 生成月份列
      const monthColumns = targetMonth.map((month, index) => ({
        label: month,
        children: [
          { label: "计划量", prop: `planAmount${index + 1}` },
          { label: "实际量", prop: `realAmount${index + 1}` },
          { label: "完成率", prop: `compRate${index + 1}` },
          { label: "上年同期", prop: `sameLYear${index + 1}` },
          { label: "变动率", prop: `changeRate${index + 1}` },
          { label: "挑战目标", prop: `goal${index + 1}` },
        ],
      }));
      this.monthColumns = monthColumns;
    },
    toIndicator(row){
      this.$router.push({
        name:"indicator",
      })
    },
    handleSearch() {
      this.getMothColumns();
      console.log(this.searchForm);
    },
    handleCellClick(row, column, cell, event) {
      console.log(row, column, cell, event);
    },
    saveRow(row) {
      console.log(row);
    },
  },
};
</script>
<style lang="scss" scoped>
.prod-track {
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-group {
      margin-bottom: 10px;
      text-align: right;
    }
  }
  .table-container {
    padding: 10px;
  }
}

.submitted {
  color: #00b42a;
}

.saved {
  color: #1677ff;
}
</style>
