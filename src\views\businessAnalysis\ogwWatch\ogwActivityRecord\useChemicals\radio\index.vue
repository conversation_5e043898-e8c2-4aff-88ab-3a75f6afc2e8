<template>
  <div class="ratio">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
      ></OgwSearch>
      <div>
        <el-button type="primary" @click="$router.go(-1)" disabled
          >导出</el-button
        >
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </div>
    </div>
    <div>
      <el-radio-group v-model="radio" @change="handleChange">
        <el-radio
          v-for="item in legendData"
          :key="item"
          :label="item"
          class="radio-item"
          >{{ item }}</el-radio
        >
      </el-radio-group>
      <RatioEcharts :chartsData="targetObj"></RatioEcharts>
    </div>
    <div class="table-box">
      <OgwTable
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-index="true"
        :actionColumnWidth="120"
        :pagination="true"
        :page-size="pageSize"
        :current-page="currentPage"
        :total="total"
        @page-change="handlePageChange"
      ></OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import RatioEcharts from "./ratioEcharts.vue";
import { getUseChemicalDetail } from "@/api/ogwActiveRecord/useChemical.js";
import { getProd } from "@/api/common.js";
import { cloneDeep } from "lodash";
export default {
  name: "ratio",
  components: {
    OgwSearch,
    OgwTable,
    RatioEcharts,
  },
  created() {
    const dateArr = [];
    dateArr[0] = new Date().getFullYear() + "-" + Number(1);
    dateArr[1] = new Date().getFullYear() + "-" + new Date().getMonth();
    this.searchForm.monthRange = dateArr;
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.getProdList();
    this.getTableData();
  },
  data() {
    return {
      prodGroup: [], //将数据分组存储
      radio: "",
      orgList: [],
      searchForm: {
        deviceNameCode: "",
        monthRange: ["", ""],
      },
      // 添加分页相关数据
      pageSize: 10,
      currentPage: 1,
      total: 0,
      allTableData: [], // 存储所有数据
      columns: [
        {
          label: "平台名称",
          prop: "productionName",
        },
        {
          label: "药剂类型",
          prop: "chemicalName",
        },
        {
          label: "截止日期",
          prop: "ym",
        },
        {
          label: "加注量(L)",
          prop: "injectionVolume",
        },
        {
          label: "库存量(L)",
          prop: "inventory",
        },
        {
          label: "采购量(L)",
          prop: "purchaseQuantity",
        },
        {
          label: "(加注量+库存量）/采购量(%)",
          prop: "radio",
        },
        {
          label: "采购量-（加注量+库存量)(L)",
          prop: "subVolume",
        },
      ],
      tableData: [],
      legendData: [], //图例数据(化学药剂)
      targetObj: {}, //图表数据
    };
  },
  computed: {
    // 计算当前页显示的数据
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allTableData.slice(start, end);
    },
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ];
    },
  },
  watch: {
    radio(val) {
      this.targetObj = this.filterDataByChemicalType(
        cloneDeep(this.allTableData), // 使用全部数据
        val
      );
    },
    allTableData(val) {
      this.total = val.length;
      this.tableData = this.paginatedTableData;
      this.targetObj = this.filterDataByChemicalType(
        cloneDeep(val),
        this.radio
      );
    },
    paginatedTableData(val) {
      this.tableData = val;
    },
    searchForm: {
      handler(val) {
        this.deviceSerOptions = this.orgList
          .filter((item) => item.orgId === val.orgId)
          .map((item) => item.children)
          .flat();
        this.searchFields[1].options = this.deviceSerOptions;
      },
      deep: true,
    },
  },
  methods: {
    handleChange(val) {
      this.radio = val;
    },
    handleSearch(value) {
      this.searchForm.deptId = value?.deptId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.monthRange = value.monthRange;
      this.currentPage = 1; // 重置到第一页
      this.getTableData();
    },
    handlePageChange({ pageSize, currentPage }) {
      this.pageSize = pageSize;
      this.currentPage = currentPage;
    },
    filterDataByChemicalType(records, chemicalAgentsType) {
      // 过滤指定药剂类型的数据
      const filteredRecords = records.filter(
        (item) => item.chemicalName === chemicalAgentsType
      );

      // 获取所有唯一的平台名称
      const productionNameArr = Array.from(
        new Set(filteredRecords.map((item) => item.productionName))
      );

      // 获取所有唯一的日期并排序
      const xData = Array.from(
        new Set(filteredRecords.map((item) => item.ym))
      ).sort();

      // 为每个平台创建两条线的数据
      const seriesData = [];

      productionNameArr.forEach((platformName) => {
        // 获取当前平台的数据
        const platformData = filteredRecords.filter(
          (item) => item.productionName === platformName
        );

        // 创建y1数据数组（radio值，左Y轴）
        const y1Data = xData.map((date) => {
          const dataPoint = platformData.find((item) => item.ym === date);
          return dataPoint ? dataPoint.radio : null;
        });

        // 创建y2数据数组（subVolume值，右Y轴）
        const y2Data = xData.map((date) => {
          const dataPoint = platformData.find((item) => item.ym === date);
          return dataPoint ? dataPoint.subVolume : null;
        });

        // 添加y1线（左Y轴）
        seriesData.push({
          name: `${platformName}-比率`,
          type: "line",
          yAxisIndex: 0, // 左Y轴
          data: y1Data,
          connectNulls: false, // 不连接空值点
        });

        // 添加y2线（右Y轴）
        seriesData.push({
          name: `${platformName}-差值`,
          type: "line",
          yAxisIndex: 1, // 右Y轴
          data: y2Data,
          connectNulls: false,
        });
      });

      return {
        xData: xData,
        seriesData: seriesData,
        chemicalAgentsType: chemicalAgentsType,
      };
    },
    async getTableData() {
      const data = {
        hzNo: this.searchForm.deviceNameCode || "",
        startDate: this.searchForm.monthRange[0] || null,
        endDate: this.searchForm.monthRange[1] || null,
      };
      const res = await getUseChemicalDetail(data);
      if (res.code === 200) {
        this.allTableData = res.data || []; // 存储全部数据
        this.total = this.allTableData.length;
        this.currentPage = 1; // 重置到第一页

        this.legendData = Array.from(
          new Set(res.data.map((item) => item.chemicalName))
        );

        this.radio = this.legendData[0];
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ratio {
  padding: 20px;
  background-color: #fff;
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .table-box {
    margin-top: 20px;
  }
}

.radio-item {
  margin-right: 10px;
}
</style>
